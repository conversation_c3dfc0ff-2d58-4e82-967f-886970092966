# 富文本模块抽取实施方案

## 1. 项目概述

### 1.1 目标
将当前项目中位于 `module-handwritingText` 模块的富文本功能（AREditText）抽取到独立的 `module-richText` 模块中，实现功能解耦和复用。

### 1.2 抽取范围
- **包含**：富文本基础功能部分（AREditText核心组件及相关基础设施）
- **不包含**：上层富文本工具栏UI部分（RichTextFormatToolbar等）

## 2. 富文本功能清单

### 2.1 核心组件
- **AREditText**: 富文本编辑器核心视图
- **RichTextController**: 富文本控制器，管理视图与ViewModel交互
- **RichTextViewHolder**: 富文本视图持有者，处理内容变化监听

### 2.2 数据模型
- **RichTextStyleEntity**: 富文本样式数据结构
- **StyleRange**: 样式范围定义
- **RichTextEditOperation**: 富文本编辑操作记录

### 2.3 样式功能（包含已注释功能）
#### 2.3.1 文本格式
- ✅ **粗体 (Bold)**: AreBoldSpan
- ✅ **斜体 (Italic)**: AreItalicSpan  
- ✅ **下划线 (Underline)**: AreUnderlineSpan
- ✅ **删除线 (Strikethrough)**: ARE_Strikethrough
- 🔄 **字体大小 (Font Size)**: AreFontSizeSpan（已注释，需恢复）
- 🔄 **字体颜色 (Font Color)**: AreForegroundColorSpan（已注释，需恢复）
- 🔄 **背景颜色 (Background Color)**: AreBackgroundColorSpan（已注释，需恢复）

#### 2.3.2 段落格式
- ✅ **无序列表 (Bullet List)**: ARE_ListBullet, ListBulletSpan
- ✅ **有序列表 (Number List)**: ARE_ListNumber, ListNumberSpan
- ✅ **待办事项 (Todo List)**: ARE_Upcoming, UpcomingListSpan
- 🔄 **对齐方式 (Alignment)**: ARE_Alignment（已注释，需恢复）
  - 左对齐 (Left Align)
  - 居中对齐 (Center Align)  
  - 右对齐 (Right Align)
- 🔄 **缩进功能 (Indent)**: ARE_IndentLeft, ARE_IndentRight（已注释，需恢复）

#### 2.3.3 其他功能
- ✅ **撤销/重做**: RichTextUndoRedoManager
- ✅ **复制/粘贴**: 内置支持
- ✅ **选择文本**: 文本选择和光标控制
- ✅ **内容限制**: 字数限制功能
- ✅ **富文本转换**: HTML与富文本格式互转

### 2.4 转换器组件
- **RichTextStyleEntityToSpanConverter**: 富文本实体到Span转换
- **RichTextStyleSpanToEntityConverter**: Span到富文本实体转换
- **RichTextBuilder**: 富文本构建器
- **MarginStyleConverter**: 边距样式转换器

### 2.5 历史记录系统
- **RichTextUndoRedoManager**: 撤销重做管理器
- **UndoStackOp**: 撤销栈操作
- **RichTextEditOperation**: 编辑操作记录

### 2.6 工具类
- **ARE_Helper**: 富文本辅助工具
- **RichTextKTUtils**: Kotlin工具扩展
- **RichTextParagraphUtil**: 段落处理工具
- **NoteContentUtil**: 内容处理工具
- **DisplayUtils**: 显示工具

## 3. 模块抽取实施步骤

### 3.1 第一阶段：创建独立模块（1人天）
1. **创建module-richText模块**
   - 配置build.gradle.kts
   - 设置模块依赖关系
   - 配置AndroidManifest.xml

2. **建立包结构**
   ```
   module-richText/
   ├── src/main/java/com/tcl/ai/note/richtext/
   │   ├── views/           # 核心视图组件
   │   ├── data/            # 数据模型
   │   ├── converter/       # 转换器
   │   ├── history/         # 历史记录
   │   ├── spans/           # 自定义Span
   │   ├── styles/          # 样式定义
   │   ├── utils/           # 工具类
   │   ├── listener/        # 监听器接口
   │   └── controller/      # 控制器
   ```

### 3.2 第二阶段：迁移核心组件（2人天）
1. **迁移AREditText及相关类**
   - AREditText.java
   - EditMovementMethod.java
   - InputConnectionCEWrapper.java
   - KeyListenerWrapper.java

2. **迁移数据模型**
   - RichTextStyleEntity.kt
   - StyleRange相关类

3. **迁移控制器组件**
   - RichTextController.kt
   - RichTextViewHolder.kt
   - StyleStatusDelegate.kt

### 3.3 第三阶段：迁移样式系统（2人天）
1. **迁移所有Span类**
   - 基础Span: ARE_Span.java, AreDynamicSpan.java
   - 文本格式Span: AreBoldSpan, AreItalicSpan, AreUnderlineSpan等
   - 段落格式Span: AreListSpan, ListBulletSpan, ListNumberSpan等
   - 颜色Span: AreForegroundColorSpan, AreBackgroundColorSpan
   - 其他Span: AreImageSpan, AreUrlSpan等

2. **迁移样式类**
   - ARE_ABS_Style.java及其子类
   - 所有具体样式实现类

### 3.4 第四阶段：迁移转换器和工具（1.5人天）
1. **迁移转换器组件**
   - RichTextStyleEntityToSpanConverter.kt
   - RichTextStyleSpanToEntityConverter.kt
   - RichTextBuilder.kt
   - MarginStyleConverter.kt

2. **迁移工具类**
   - ARE_Helper.java
   - RichTextKTUtils.kt
   - RichTextParagraphUtil.java
   - DisplayUtils.kt
   - NoteContentUtil.kt

### 3.5 第五阶段：迁移历史记录系统（1人天）
1. **迁移撤销重做功能**
   - RichTextUndoRedoManager.kt
   - RichTextEditOperation.kt
   - UndoStackOp.kt

2. **迁移监听器接口**
   - StyleStatusListener.java
   - 其他相关监听器

### 3.6 第六阶段：恢复注释功能（2人天）
1. **恢复字体大小功能**
   - 启用AreFontSizeSpan
   - 恢复ARE_FontSize样式类
   - 添加字体大小选择逻辑

2. **恢复颜色功能**
   - 启用颜色相关Span
   - 恢复颜色选择器
   - 实现颜色应用逻辑

3. **恢复对齐功能**
   - 启用ARE_Alignment
   - 实现左中右对齐
   - 添加对齐状态管理

4. **恢复缩进功能**
   - 启用ARE_IndentLeft/Right
   - 实现缩进逻辑
   - 添加缩进控制

### 3.7 第七阶段：适配和测试（2人天）
1. **更新原项目调用**
   - 修改module-handwritingText中的引用
   - 更新import语句
   - 调整依赖关系

2. **集成测试**
   - 功能完整性测试
   - 性能测试
   - 兼容性测试

### 3.8 第八阶段：文档和优化（0.5人天）
1. **编写使用文档**
   - API文档
   - 集成指南
   - 示例代码

2. **代码优化**
   - 清理无用代码
   - 优化性能
   - 代码规范检查

## 4. 当前项目调用富文本模块方法

### 4.1 依赖配置
```kotlin
// module-handwritingText/build.gradle.kts
dependencies {
    implementation(project(":module-richText"))
    // 移除原有富文本相关代码
}
```

### 4.2 使用方式
```kotlin
// 原有调用方式保持不变，只需更新import
import com.tcl.ai.note.richtext.controller.RichTextController
import com.tcl.ai.note.richtext.views.AREditText
import com.tcl.ai.note.richtext.data.RichTextStyleEntity

// 在RichTextViewModel2中使用
class RichTextViewModel2 {
    private val richTextController = RichTextController(context, this)
    
    fun attachToViewGroup(viewGroup: ViewGroup) {
        richTextController.attachViewGroup(viewGroup)
    }
}
```

### 4.3 接口适配
```kotlin
// 保持现有接口不变，确保向后兼容
interface RichTextInterface {
    fun setContent(content: String, styleEntity: RichTextStyleEntity)
    fun getContent(): Pair<String, RichTextStyleEntity>
    fun applyStyle(styleType: String, value: Any)
    fun undo(): Boolean
    fun redo(): Boolean
}
```

## 5. 工作量估算

| 阶段 | 任务内容 | 预估工作量 | 风险等级 |
|------|----------|------------|----------|
| 1 | 创建独立模块 | 1人天 | 低 |
| 2 | 迁移核心组件 | 2人天 | 中 |
| 3 | 迁移样式系统 | 2人天 | 中 |
| 4 | 迁移转换器和工具 | 1.5人天 | 低 |
| 5 | 迁移历史记录系统 | 1人天 | 低 |
| 6 | 恢复注释功能 | 2人天 | 高 |
| 7 | 适配和测试 | 2人天 | 中 |
| 8 | 文档和优化 | 0.5人天 | 低 |
| **总计** | **完整抽取** | **12人天** | **中等** |

### 5.1 风险评估
- **高风险**：恢复注释功能可能涉及复杂的样式逻辑重构
- **中风险**：模块间依赖关系调整，可能影响现有功能
- **低风险**：纯代码迁移，风险可控

### 5.2 里程碑
- **第1周**：完成阶段1-4（基础迁移）
- **第2周**：完成阶段5-7（功能完善和测试）
- **第3周**：完成阶段8（文档和发布）

## 6. 预期收益

### 6.1 技术收益
- **模块解耦**：富文本功能独立，便于维护
- **代码复用**：其他项目可直接使用富文本模块
- **功能完整**：恢复所有注释的富文本功能

### 6.2 开发效率
- **并行开发**：富文本功能可独立开发测试
- **版本管理**：独立版本控制，降低冲突
- **测试隔离**：单元测试更加专注

### 6.3 维护成本
- **职责清晰**：富文本相关问题定位更准确
- **升级便利**：富文本功能升级不影响其他模块
- **文档完善**：独立模块文档更加详细

## 7. 注意事项

### 7.1 兼容性
- 确保API向后兼容
- 保持现有功能不受影响
- 渐进式迁移，避免大规模重构

### 7.2 性能
- 注意模块间通信开销
- 优化富文本渲染性能
- 内存使用优化

### 7.3 测试
- 完整的单元测试覆盖
- 集成测试验证
- 性能基准测试

---

*本文档版本：v1.0*  
*创建日期：2025-08-21*  
*预计完成时间：3周（12个工作日）*
